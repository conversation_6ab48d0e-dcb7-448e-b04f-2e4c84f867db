import docx
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.ns import qn

def create_word_document():
    # 创建文档
    doc = Document()
    
    # 设置中文字体
    doc.styles['Normal'].font.name = '宋体'
    doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
    
    # 添加引言部分
    intro_heading = doc.add_heading('引言', level=1)
    intro_content = [
        "**引言**：对植物水分吸收以及运输的重要性及其研究进展进行概述，并提出相应的研究目标。",
        "**根系结构与吸水的功能适应性**：对不同类型根系的形态和功能适应性进行分析，并运用表格来对比各类根系的特性。",
        "**根系水分吸收的生理机制**：阐述主动与被动这两种吸收机制，并讨论水力再分配以及水孔蛋白的调控作用，内容囊括了数据分析以及表格。",
        "**水分径向运输的细胞路径**：对跨细胞、共质体以及细胞间隙这三种运输途径的特点及其调控机制进行比较。",
        "**茎叶水分运输与分配机制**：分析木质部运输的驱动力、韧皮部所参与的作用以及叶片的水分分配，内容囊括了最新的研究成果。",
        "**生态适应性与农业应用**：对植物的抗旱机制以及农业节水技术的应用进行总结，并运用表格来对比不同技术的效果。",
        "**研究挑战与未来展望**：提出当前研究当中所面临的四个关键挑战以及未来的发展方向。"
    ]
    
    for item in intro_content:
        p = doc.add_paragraph()
        # 处理加粗文本
        if item.startswith("**") and "**：" in item:
            parts = item.split("**：")
            bold_text = parts[0].replace("**", "")
            normal_text = parts[1]
            run1 = p.add_run(bold_text + "：")
            run1.bold = True
            p.add_run(normal_text)
        else:
            p.add_run(item)
    
    # 添加标题
    title = doc.add_heading('根系水分吸收与运输机制的生理生态基础以及农业应用', level=1)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加摘要
    doc.add_heading('摘要', level=2)
    abstract = ("植物根系所进行的水分吸收以及运输，可以说是陆地生态系统水分循环当中的一个核心环节，"
                "并且它也是决定作物水分利用效率的一个关键因素。这篇论文系统性地综述了水分吸收的生理机制、"
                "径向运输的途径以及长距离运输调控方面的最新研究进展。研究结果表明，根系可以借助水孔蛋白活性的调节"
                "以及水力再分配这个现象，来动态地优化自身的吸水效率；在干旱的条件下，运用纳米材料可以增强抗氧化酶的活性，"
                "同时去调控像PIP27这类水通道蛋白的表达，从而使得菊芋根系的导水率得以提升34.7%；"
                "木质部的运输工作存在着连续水柱整体提升以及不连续水柱分段运输这两种模式，"
                "而日间木质部与韧皮部之间还存在一种渗透驱动型的径向水流，这一点通过同位素示踪得到了证实，"
                "其δ18O的差异达到了25.2‰。在生态适应性这个方面，荒漠植物胡杨凭借水力再分配，"
                "补充了林分蒸散发当中37%的水分消耗。鉴于此，本文提出了像根系构型优化以及土壤改良剂应用这样的农业节水策略，"
                "为应对全球水资源短缺的这个情况，去提供理论上的支持以及技术上的路径。")
    doc.add_paragraph(abstract)
    
    # 1. 引言
    doc.add_heading('1 引言', level=2)
    intro_text = [
        ("水分可以说是植物生长发育的一个决定性因子，而根系被当作是水分吸收的核心器官，"
         "它的结构以及功能会直接地影响到植物的水分利用效率。在土壤-植物-大气连续体也就是SPAC当中，"
         "水分需要去克服重力、摩擦阻力以及水势梯度，并且经历一个从根土界面吸收、径向跨组织运输到轴向长距离运输的复杂过程，"
         "最终才参与到光合作用以及蒸腾作用当中。根据估算，一株成熟的玉米每天需要吸收200升的水，"
         "在这当中，有95%以上的水分是凭借蒸腾作用散失掉的，这个过程既维持了植物的热平衡，"
         "也驱动了水分以及养分的上行运输。"),
        
        ("近些年以来，随着像热比率法、稳定同位素示踪、多模态成像这类技术的创新应用，"
         "学术界对于根系吸水机制的认识也得到了不断的深化。传统的理论主要强调蒸腾拉力以及根压所起的主导作用，"
         "不过最新的研究发现，根系水力再分配、水通道蛋白的门控调节以及茎干的蓄水能力等因素，"
         "会共同地构成一个动态的调控网络。尤其是在全球气候变化的这个大背景之下，干旱胁迫的加剧，"
         "使得对于植物水分运输机制的解析工作，成为了农业节水的一个理论基础。"),
        
        ("本文对根系吸水、径向运输、轴向运输以及生态适应性方面的最新研究成果进行了系统性的整合，"
         "重点阐述的内容囊括了：(1) 根系形态结构与吸水功能之间的匹配机制；(2) 水分跨细胞运输的生理调控；"
         "(3) 茎叶水分的分配规律；(4) 抗旱适应性策略及其在农业上的应用。"
         "通过揭示多尺度水分运输的协同机制，来为作物遗传改良以及栽培技术的创新提供理论上的支撑。")
    ]
    
    for text in intro_text:
        doc.add_paragraph(text)
    
    # 2. 根系结构与吸水的功能适应性
    doc.add_heading('2 根系结构与吸水的功能适应性', level=2)
    
    # 2.1
    doc.add_heading('2.1 根系构型与水分捕获', level=3)
    section_2_1_text1 = ("植物在长期的进化过程当中，形成了多样化的根系构型，来优化其在不同水分环境下的吸收效率。"
                        "一年生的草本植物，通常会形成一种浅层密集型的根系，其特性是主根退化而侧根发达，"
                        "有80%以上的根量会分布在0到30厘米的土层当中，比如小麦的根系总长可以达到每立方米70公里，"
                        "这样的构型有利于去快速吸收浅层土壤当中的水分。和这个情况相反，木本植物大多会发展出深直根系统，"
                        "像是荒漠灌木柽柳（Tamarix ramosissima），它的主根可以下探到15米深，一直达到地下水层，"
                        "其深层根的占比超过了总生物量的40%。这种所谓的"双层面策略"，使得植物既可以运用降水所形成的浅层土壤水，"
                        "又能够获取到稳定的深层水源。")
    
    section_2_1_text2 = ("不定根在水分吸收这个过程当中的作用，在很长一段时间里是被低估了的。"
                        "番茄的水培实验结果表明，要是茎部形成了不定根，那么在距离地面60厘米以上的区域，"
                        "茎干的水力导度会得到显著的提升，植株的总吸水量会增加23%，同时果实的产量也提高了31%。"
                        "这个现象在淹水或者高湿的环境当中尤为关键，它说明不定根不仅仅是一个应急的吸水器官，"
                        "更可以说是水分运输系统当中的一个结构性补偿机制。")
    
    doc.add_paragraph(section_2_1_text1)
    doc.add_paragraph(section_2_1_text2)
    
    # 2.2
    doc.add_heading('2.2 根际微域与土壤界面', level=3)
    section_2_2_text1 = ("根土界面可以说是水分进行跨系统运输的第一道屏障。通过扫描电镜的观察可以发现，"
                        "根毛的密度以及黏液的分泌量，会直接地影响到根土接触的效率：玉米根毛区的表皮细胞每平方毫米可以分泌0.8微克的多糖类黏液，"
                        "去填充根土之间的间隙直到5微米以下，从而使得导水率得以提升3倍。在干旱的条件下，"
                        "根毛的密度会呈现出指数级的增长，比如大豆在0.5 MPa水势下，它的根毛数量会增加240%，"
                        "长度也会延伸35%，这样就极大程度上扩大了吸水的表面积。")
    
    section_2_2_text2 = ("菌根共生是另外一个重要的适应策略。丛枝菌根真菌即AMF，凭借着它的菌丝网络，"
                        "把吸水范围扩展到了根系本体难以到达的区域当中。实验结果证明，在接种了Glomus intraradices的柑橘植株上，"
                        "它的有效吸水距离从根系本体的2毫米延伸到了菌丝可以到达的6毫米，这也就相当于把根土接触的体积扩大了9倍。"
                        "尤其在砂质土壤当中，菌丝网络可以降低30%的土壤水分渗漏损失，显著地提升了水分的滞留能力。")
    
    doc.add_paragraph(section_2_2_text1)
    doc.add_paragraph(section_2_2_text2)
    
    # 添加表1
    p = doc.add_paragraph()
    run = p.add_run('表1：不同植物的根系形态针对于水分吸收所表现出的适应性特性')
    run.bold = True
    
    table1 = doc.add_table(rows=5, cols=5)
    table1.style = 'Table Grid'
    
    # 表头
    headers = ['植物类型', '典型根系构型', '主要吸水区', '根冠比', '环境适应性']
    for i, header in enumerate(headers):
        table1.rows[0].cells[i].text = header
    
    # 表格数据
    data = [
        ['荒漠柽柳', '深直根+水平侧根', '深层根(>5m)', '0.83', '地下水利用'],
        ['温带禾本科', '浅层纤维根', '030cm土层', '0.25', '降水利用'],
        ['热带雨林乔木', '板根+气生根', '枯落物层', '0.31', '表层水捕获'],
        ['湿地植物', '不定根+通气组织', '淹水茎基部', '0.19', '缺氧环境适应']
    ]
    
    for i, row_data in enumerate(data, 1):
        for j, cell_data in enumerate(row_data):
            table1.rows[i].cells[j].text = cell_data
    
    # 3. 根系水分吸收的生理机制
    doc.add_heading('3 根系水分吸收的生理机制', level=2)
    
    # 3.1
    doc.add_heading('3.1 主动吸收与被动吸收', level=3)
    section_3_1_text1 = ("植物根系的吸水工作存在着主动与被动这两种机制。主动吸水是依赖渗透压差来驱动的，"
                        "由根系的代谢活动所产生。当根皮层细胞积累了像K⁺、蔗糖这类溶质的时候，"
                        "细胞的水势可以降低到0.3 MPa以下，从而促使土壤水顺着水势梯度进入。"
                        "这个过程在蒸腾微弱的夜间占据了主导地位，并形成了根压，表现为伤流现象。"
                        "葡萄茎干的伤流量可以达到每小时5毫升，为初春时节的芽萌发提供了关键的水源。")
    
    section_3_1_text2 = ("被动吸水则是由蒸腾拉力来主导的，占据了植物吸水总量的85%以上。"
                        "蒸腾作用使得叶片气孔下腔的水势降低到2.0至3.0 MPa，并凭借木质部连续水柱所产生的张力传导至根系。"
                        "在这个时候，根表皮细胞就变成了一个被动的过滤膜，水分会沿着水势梯度从土壤（约为-0.1 MPa）"
                        "经过皮层向中柱进行迁移。研究结果表明，在蒸腾旺盛的时候，大豆根系的被动吸水量可以达到主动吸水的20倍之多。")
    
    doc.add_paragraph(section_3_1_text1)
    doc.add_paragraph(section_3_1_text2)
    
    # 3.2
    doc.add_heading('3.2 水力再分配及其生态效应', level=3)
    section_3_2_text1 = ("水力再分配也就是HR，是根系用来应对水分空间异质性的一个重要策略。"
                        "在黑河下游的荒漠河岸林，胡杨（Populus euphratica）的根系会在夜间把深层水分提升到浅层的干土当中"
                        "（也就是水力提升，HL），而在降水之后又会把表层水输送到深层（也就是水力下传，HD）。"
                        "运用热比率法进行的测定显示，胡杨生长季的HR通量为每天0.16到0.26毫米，"
                        "而柽柳则达到了每天0.14到1.02毫米。这种双向的调节工作显著地改善了根际的水分平衡："
                        "柽柳林在干季的夜间，凭借HR补充了表层土壤71%的日间水分损耗，使得土壤储水枯竭的时间推迟了4天。")
    
    section_3_2_text2 = ("HR对于植物水分利用效率的提升，主要表现在三个方面：(1) 维持细根的活性，"
                        "这使得柽柳在降水脉冲后的24小时之内就能够启动吸水响应；(2) 直接地支持蒸腾作用，"
                        "胡杨的HR对于蒸腾的平均贡献率达到了38.75%；(3) 调节群落的水分格局，"
                        "胡杨林分的HR总计（123毫米）占据了蒸散发（313毫米）的37%，从而形成了局地的小气候，"
                        "使得林内的日间温度比柽柳林要低4℃，相对湿度则高出10%。")
    
    doc.add_paragraph(section_3_2_text1)
    doc.add_paragraph(section_3_2_text2)
    
    # 3.3
    doc.add_heading('3.3 水孔蛋白的调控机制', level=3)
    section_3_3_text1 = ("水通道蛋白即AQPs，是水分进行跨膜运输的关键通道，它的活性调控工作构成了根系吸水的分子开关。"
                        "在一项关于菊芋（Helianthus tuberosus）干旱胁迫的研究当中，通过叶面喷施100 mg·L⁻¹的纳米硅（SiNP100），"
                        "使得POD、CAT以及SOD这些抗氧化酶的活性分别提高了27.87%、16.07%和6.70%，从而降低了ROS的积累；"
                        "同时还特异性地上调了PIP27以及TIP27基因的表达，幅度达到了709%和707%，而下调了像TIP15这样的亚型。"
                        "这种选择性的调控使得根系的导水率（Lpr）得以提升34.67%，束缚水的含量也增加了44.29%。")
    
    section_3_3_text2 = ("水孔蛋白的活性是受到磷酸化/去磷酸化这个动态过程来调节的。"
                        "磷酸化的PIP2;1蛋白使得它的Ser283位点带上了负电荷，从而引起蛋白构象的变化，"
                        "通道的直径从3.0 Å扩展到了3.4 Å，极大地提升了水分的通透性。反过来说，"
                        "要是胞质的pH值降到6.0以下，或者[Ca²⁺]的浓度超过1 μM，通道就会收缩甚至关闭。"
                        "这种精密的调控使得根系能够马上响应环境的变化：玉米的根在30秒内就可以完成AQPs活性的调节工作，"
                        "让水分通量改变3倍以上。")
    
    doc.add_paragraph(section_3_3_text1)
    doc.add_paragraph(section_3_3_text2)
    
    # 添加表2
    p = doc.add_paragraph()
    run = p.add_run('表2：植物主要水孔蛋白的类型及其功能特性')
    run.bold = True
    
    table2 = doc.add_table(rows=6, cols=5)
    table2.style = 'Table Grid'
    
    # 表头
    headers2 = ['类型', '亚细胞定位', '渗透系数(Pf)', '调控机制', '胁迫响应']
    for i, header in enumerate(headers2):
        table2.rows[0].cells[i].text = header
    
    # 表格数据
    data2 = [
        ['PIP1;1', '质膜外侧', '1530 μm/s', 'pH依赖性', '干旱下调'],
        ['PIP2;2', '质膜内侧', '5080 μm/s', '磷酸化激活', '干旱上调'],
        ['TIP1;1', '液泡膜', '1020 μm/s', '钙离子抑制', '盐胁迫诱导'],
        ['TIP2;1', '液泡膜', '4060 μm/s', '渗透压调节', '水淹上调'],
        ['NIP1;1', '内质网膜', '<5 μm/s', '重金属诱导', '砷胁迫激活']
    ]
    
    for i, row_data in enumerate(data2, 1):
        for j, cell_data in enumerate(row_data):
            table2.rows[i].cells[j].text = cell_data
    
    # 4. 水分径向运输的细胞路径
    doc.add_heading('4 水分径向运输的细胞路径', level=2)
    
    # 4.1
    doc.add_heading('4.1 跨细胞途径与共质体途径', level=3)
    section_4_1_text1 = ("水分从根表皮到木质部的径向运输存在着三条路径：跨细胞途径（transcellular）、"
                        "共质体途径（symplastic）以及细胞间隙途径（apoplastic）。"
                        "跨细胞途径是依赖水孔蛋白介导的跨膜运输来实现的，具有高度的可调控性，"
                        "在水分运输总量当中占据了35%到70%。共质体途径是依靠胞间连丝来实现细胞间的水分运动，"
                        "其运输速率受到了连丝孔径（大约2.5纳米）以及密度的限制。"
                        "在水分充足的时候，拟南芥根尖的共质体运输占比可以达到60%，不过在盐胁迫下会下降到20%以下。")
    
    section_4_1_text2 = ("内皮层的凯氏带是细胞间隙途径的一个物理屏障。凯氏带是由木质素以及疏水蛋白所构成的，"
                        "其宽度大约在5到8微米，有效地阻隔了质外体的水分直接进入中柱。然而，"
                        "在侧根发生的区域存在着结构的间断，这就成为了水分运输的一个"后门通道"。"
                        "磁共振成像也就是MRI显示，玉米根中大约有15%的水分是借助这个通道绕过了凯氏带屏障的。")
    
    doc.add_paragraph(section_4_1_text1)
    doc.add_paragraph(section_4_1_text2)
    
    # 4.2
    doc.add_heading('4.2 木质部装载与空穴修复', level=3)
    section_4_2_text1 = ("水分在进入木质部导管之后，会面临着空穴化（cavitation）的风险。"
                        "要是木质部的张力超过了3.0 MPa，那么水中溶解的气体就会析出并形成空穴，"
                        "从而阻断水柱的连续性。传统的内聚力学说认为，连续水柱的断裂将会导致水分运输的停止，"
                        "不过双渗透泵模型提出了一个新的解释：当蒸腾速率大于吸水速率时，"
                        "不连续的水柱会以分段提升的方式进行运输，每一段顶端的凹面所产生的毛细力（可以达到0.2 MPa）"
                        "会与重力相平衡，使得水分呈现出阶梯式的上行。")
    
    section_4_2_text2 = ("植物进化出了多种多样的空穴修复机制：(1) 根压驱动的修复：葡萄茎干在夜间的根压可以达到0.1 MPa，"
                        "能够有效地溶解掉小的空穴；(2) 凝胶分泌物的填充：胡桃的木质部在受损处会分泌多糖凝胶来填充空腔；"
                        "(3) 侧向的水分输入：日本扁柏的枝条凭借木质部与韧皮部之间的径向水流来补充水分。"
                        "同位素示踪的结果证实，在日间枝条收缩期，未富集同位素的木质部水（δ18O=-6.6‰）"
                        "与叶片富集水（δ18O=+18.9‰）混合后进入韧皮部，从而维持了水柱的连续性。")
    
    doc.add_paragraph(section_4_2_text1)
    doc.add_paragraph(section_4_2_text2)
    
    # 5. 茎叶水分运输与分配机制
    doc.add_heading('5 茎叶水分运输与分配机制', level=2)
    
    # 5.1
    doc.add_heading('5.1 木质部上行运输的驱动力', level=3)
    section_5_1_text1 = ("木质部水分的上行运输是依赖于蒸腾拉力-内聚力-张力这个机制（Cohesion-Tension Theory）的。"
                        "对银杏树干液流的监测显示，液流的速率在每小时0.82到20.52厘米之间，"
                        "和蒸腾强度呈现出显著的正相关关系。空气温度每上升1℃，液流速率平均会增加8.6%；"
                        "而空气相对湿度每上升10%，液流速率则会下降27.3%。"
                        "这种变化源于气孔的开度对于大气蒸汽压亏缺也就是VPD的响应："
                        "当VPD从1.0 kPa上升到3.0 kPa时，北美红杉的气孔导度会下降60%，"
                        "以此来避免木质部张力的过度升高。")
    
    section_5_1_text2 = ("茎干被当作是水分运输的中继站，同时拥有运输通道以及蓄水库这双重功能。"
                        "银杏树干的水分动态表明，液流的增加会伴随着茎干水分含量的下降，"
                        "茎干水分的导数与液流数据呈现出显著的负相关（Pearson r ≤ -0.7）。"
                        "这种所谓的"蓄水-释放"缓冲机制使得植物能够应对短暂的干旱："
                        "苹果树干可以储存当日蒸腾量的25%到40%，并在午间蒸腾高峰时去补充水分。")
    
    doc.add_paragraph(section_5_1_text1)
    doc.add_paragraph(section_5_1_text2)
    
    # 5.2
    doc.add_heading('5.2 韧皮部与叶脉网络的水分分配', level=3)
    section_5_2_text1 = ("传统的理论认为水分运输仅仅是由木质部来承担的，不过对日本扁柏的研究揭示了韧皮部在水分分配当中的作用。"
                        "借助δ18O以及氘标记的双重实验，研究人员发现，在日间枝条收缩期间，"
                        "木质部的水分会向韧皮部进行径向的转移。同位素的检测显示，如果不存在水分混合，"
                        "那么韧皮部水的δ18O值应该接近于叶片富集水（+18.9‰），但是实际测得的值（-6.3‰）"
                        "却更接近木质部水（-6.6‰），这就证实了存在一种渗透驱动型的径向传输。"
                        "这个过程是由韧皮部进行蔗糖装载时所产生的渗透压来驱动的，"
                        "它使得水分在木质部以及韧皮部之间进行了重新分配。")
    
    section_5_2_text2 = ("叶片的水分分配呈现出一种层级式的网络结构。主脉的导管直径大约是100微米，"
                        "次级脉为20到50微米，而三级脉仅有5到10微米。"
                        "这样的结构使得水分会优先供应给顶端的分生组织：玉米叶片顶端的水势要比基部高出0.4 MPa，"
                        "从而确保了生长中心的水分优先权。气孔作为水分的出口，它的分布密度和叶脉密度呈现出正相关关系："
                        "拟南芥的突变体veinless，其叶脉密度降低了40%，相应的气孔密度也减少了35%，"
                        "这体现了结构与功能之间的协同进化。")
    
    doc.add_paragraph(section_5_2_text1)
    doc.add_paragraph(section_5_2_text2)
    
    # 6. 生态适应性与农业应用
    doc.add_heading('6 生态适应性与农业应用', level=2)
    
    # 6.1
    doc.add_heading('6.1 干旱胁迫响应机制', level=3)
    section_6_1_text1 = ("荒漠植物在长期的进化当中，形成了多重的抗旱适应策略。"
                        "柽柳凭借水力再分配来吸收深层的水分以及浅层的养分，并维持着较高的蒸腾速率，"
                        "成为了所谓的"耗水型旱生植物"；而在干旱时则会关闭气孔，在厚角质层的保护之下去把蒸腾降到最低，"
                        "转变为"节水型植物"。同时，过量的光合产物会被转运到根部去促进生长，使得根冠比增大，"
                        "这种表型上的可塑性使得柽柳能够在中亚的干旱区广泛分布。")
    
    section_6_1_text2 = ("作物的抗旱性涉及到了渗透调节以及细胞保护这双重机制。在干旱胁迫之下，"
                        "菊芋会积累像脯氨酸、甜菜碱这类渗透调节物质，把细胞水势降低到-1.8 MPa；"
                        "同时还会激活SOD、POD等抗氧化酶来清除ROS。运用纳米硅进行处理可以强化这个过程："
                        "SiNP100使得H₂O₂以及O₂⁻含量降低了27.92%和27.36%，维持了细胞膜的完整性，"
                        "其MDA含量仅为干旱对照组的54%。")
    
    doc.add_paragraph(section_6_1_text1)
    doc.add_paragraph(section_6_1_text2)
    
    # 6.2
    doc.add_heading('6.2 农业节水技术应用', level=3)
    doc.add_paragraph('基于水分运输机制来开展的农业创新技术，显著地提升了水分利用效率也就是WUE：')
    
    # 添加列表项
    list_items = [
        ("**土壤结构改良**：添加2%的菱沸石使得砂壤土的饱和含水量（θs）从0.520提升到了0.542 cm³ cm⁻³，"
         "其有效水含量（AWC）增加了8.2%。在与海藻提取物联合运用之后，生菜的光合速率提升了100%，"
         "叶面积增长了33.3%，WUEi也提高了105%（从1.9提高到3.9 μmol CO₂ mmol H₂O⁻¹）。"),
        
        ("**纳米材料应用**：通过叶面喷施100 mg·L⁻¹的纳米硅（SiNP100），"
         "使得菊芋根系的导水率得以提升34.67%，块茎的产量也增加了29%。"
         "该处理特异性地上调了像PIP27这类水通道蛋白的基因，从而优化了水分的运输效率。"),
        
        ("**根系构型调控**：运用深松耕打破了犁底层，促进了玉米根系下扎到1.5米以下，"
         "使得拔节期深层根的占比提高了40%，从而让灌水效率得以提升25%。")
    ]
    
    for item in list_items:
        p = doc.add_paragraph(style='List Bullet')
        if item.startswith("**") and "**：" in item:
            parts = item.split("**：")
            bold_text = parts[0].replace("**", "")
            normal_text = parts[1]
            run1 = p.add_run(bold_text + "：")
            run1.bold = True
            p.add_run(normal_text)
        else:
            p.add_run(item)
    
    # 添加表3
    p = doc.add_paragraph()
    run = p.add_run('表3：基于水分运输机制的农业节水技术效果比较')
    run.bold = True
    
    table3 = doc.add_table(rows=6, cols=5)
    table3.style = 'Table Grid'
    
    # 表头
    headers3 = ['技术类型', '代表措施', '作用机制', 'WUE提升幅度', '适宜作物']
    for i, header in enumerate(headers3):
        table3.rows[0].cells[i].text = header
    
    # 表格数据
    data3 = [
        ['土壤改良剂', '2%菱沸石+海藻提取物', '增加土壤持水，降低气孔阻力', '105%', '设施蔬菜'],
        ['纳米材料', '100 mg·L⁻¹纳米硅', '增强抗氧化力，上调AQPs表达', '67%', '块茎作物'],
        ['物理破眠', '种脐靶向处理', '扩大种脐裂隙，激活维管束输水', '40%', '硬实种子'],
        ['耕作优化', '深松耕+垂直根引导', '促进深层根系发育', '25%', '大田作物'],
        ['水分再分配', '隔沟灌溉', '诱导水力提升，湿润深层土壤', '30%', '果园']
    ]
    
    for i, row_data in enumerate(data3, 1):
        for j, cell_data in enumerate(row_data):
            table3.rows[i].cells[j].text = cell_data
    
    # 7. 研究挑战与未来展望
    doc.add_heading('7 研究挑战与未来展望', level=2)
    doc.add_paragraph('尽管植物水分运输的研究取得了显著的进展，但仍然存在着诸多的挑战：')
    
    # 添加挑战列表
    challenges = [
        ("**空穴修复机制的争议**：关于空穴修复是否依赖根压这个问题，目前还没有一个定论。"
         "像银杏这类树种在没有根压的条件下仍然能够修复空穴，这就暗示着可能存在像茎干局部增压这样的未知机制。"
         "需要去开发纳米级的传感器来实时监测空穴的动态，并结合微CT技术来构建三维的空穴网络模型。"),
        
        ("**水孔蛋白协同调控网络**：目前我们只了解少数PIP亚型的功能，各个亚型之间的协同以及拮抗关系尚未被阐明。"
         "未来需要去结合单细胞测序以及蛋白质互作组学，来解析AQPs在细胞分化不同阶段的表达调控网络。"),
        
        ("**多尺度水分运输模型的整合**：现有的模型大多聚焦于单一的尺度，比如根系尺度的吸水模型或者是茎干尺度的液流模型。"
         "当前亟需去发展根系-茎干-叶片耦合的模型，把微观的分子调控（比如AQPs的门控）与宏观的水分运输"
         "（比如林分的蒸散）统一到一个动态的框架当中。"),
        
        ("**抗逆种质创新技术的瓶颈**：传统的育种方法对于根系性状的选择效率比较低下。"
         "运用基因编辑技术（比如CRISPR-Cas9靶向编辑PIP27的启动子）并结合高通量的表型平台"
         "（比如运用MRI进行根系原位成像），有望能够突破抗旱育种的瓶颈。")
    ]
    
    for challenge in challenges:
        p = doc.add_paragraph(style='List Bullet')
        if challenge.startswith("**") and "**：" in challenge:
            parts = challenge.split("**：")
            bold_text = parts[0].replace("**", "")
            normal_text = parts[1]
            run1 = p.add_run(bold_text + "：")
            run1.bold = True
            p.add_run(normal_text)
        else:
            p.add_run(challenge)
    
    doc.add_paragraph(("未来的研究工作应该把重点放在三个方向上：(1) 解析水力信号在器官之间传递的分子载体，"
                      "特别是像CLE25这类多肽激素，在根冠合成之后经由木质部汁液运输到叶片气孔的调控路径；"
                      "(2) 探索微生物与根系互作对于水分运输的影响，比如菌根真菌所分泌的Myc因子对水孔蛋白活性的调控作用；"
                      "(3) 开发仿生的水分管理系统，比如模拟植物的分段运输机制来设计微灌溉的管网，"
                      "或者借鉴种脐裂隙的"链式吸水"原理来改进节水播种技术。"))
    
    doc.add_paragraph(("本篇论文综合分析了植物根系水分吸收与运输的生理机制、生态适应性以及农业应用，"
                      "重点阐述了水力再分配、水孔蛋白调控、茎干蓄水等关键过程的最新研究进展。"
                      "通过揭示多尺度水分运输的协同机制，为作物的遗传改良以及栽培技术的创新提供了理论上的支撑。"
                      "在全球气候变化以及水资源短缺的这个背景之下，深入地理解植物水分运输机制，"
                      "对于发展高效节水农业以及生态修复技术具有重要的实践意义。"
                      "未来的研究工作需要在空穴修复、水孔蛋白调控网络等方向上取得突破，"
                      "并且去推动分子生物学与生态水文学的跨学科融合。"))
    
    # 保存文档
    doc.save('植物根系水分吸收与运输机制研究.docx')
    print("Word文档已生成：植物根系水分吸收与运输机制研究.docx")

# 执行函数
if __name__ == "__main__":
    create_word_document()